import React from 'react';
import { Badge } from "@/components/ui/badge";
import { useConfig } from './ConfigContext';
import { cn } from "@/src/background/util";

export const ModelDisplay = () => {
  const { config } = useConfig();
  const selectedModel = config?.selectedModel;

  if (!selectedModel) {
    return (
      <Badge className={cn(
        "bg-secondary/50 text-secondary-foreground border-border",
        "text-apple-footnote font-medium px-2 py-1 h-7"
      )}>
        No Model
      </Badge>
    );
  }

  return (
    <Badge className={cn(
      "bg-primary/10 text-primary border-primary/20",
      "text-apple-footnote font-medium px-2 py-1 h-7"
    )}>
      {selectedModel}
    </Badge>
  );
};
