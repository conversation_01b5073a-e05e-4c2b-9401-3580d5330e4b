import type { FC } from 'react';
import {
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { ConnectOllama } from './ConnectOllama';
import { ModelSelector } from './ModelSelector';
import { SettingTitle } from './SettingsTitle';
import { cn } from "@/src/background/util";

type ConnectionProps = {
  title: string;
  Component: FC<unknown>;
};

const ConnectionSection: FC<ConnectionProps> = ({
  title,
  Component,
}) => (
  (<div className="px-4 py-3 border-b border-[var(--text)]/10 last:border-b-0">
    <div className="flex items-center justify-between mb-2">
      <h4 className="text-base font-medium capitalize text-foreground">
        {title}
      </h4>
    </div>
    <Component />
  </div>)
);

export const Connect: FC = () => {

  return (
    <AccordionItem
      value="connect"
      className={cn(
        "bg-[var(--input-background)]",
        "border-[var(--text)]/10",
        "rounded-xl",
        "shadow-md",
        "transition-all duration-150 ease-in-out", 
        "hover:border-[var(--active)] hover:brightness-105",
        "overflow-hidden"
      )}
    >
      <AccordionTrigger
        className={cn(
          "flex items-center justify-between w-full px-3 py-2 hover:no-underline",
          "text-[var(--text)] font-medium",
          "hover:brightness-95",
        )}
      >
        <SettingTitle icon="♾️" text="API Access" />
      </AccordionTrigger>
      <AccordionContent className="p-0 text-[var(--text)]">
        <ConnectionSection Component={ConnectOllama} title="Ollama" />
        <ConnectionSection Component={ModelSelector} title="Model Selection" />
      </AccordionContent>
    </AccordionItem>
  );
};