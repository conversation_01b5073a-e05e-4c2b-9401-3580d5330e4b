(()=>{var e={140:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DIFF_STATUS_UPDATED=t.DIFF_STATUS_REMOVED=t.DIFF_STATUS_KEYS_UPDATED=t.DIFF_STATUS_ARRAY_UPDATED=void 0,t.DIFF_STATUS_UPDATED="updated",t.DIFF_STATUS_REMOVED="removed",t.DIFF_STATUS_KEYS_UPDATED="updated_keys",t.DIFF_STATUS_ARRAY_UPDATED="updated_array"},368:e=>{var t=9007199254740991,r=/^(?:0|[1-9]\d*)$/,n=Object.prototype,o=n.hasOwnProperty,i=n.toString,a=n.propertyIsEnumerable,c=Math.max;function u(e,t,r){var n=e[t];o.call(e,t)&&f(n,r)&&(void 0!==r||t in e)||(e[t]=r)}function s(e,n){return!!(n=null==n?t:n)&&("number"==typeof e||r.test(e))&&e>-1&&e%1==0&&e<n}function f(e,t){return e===t||e!=e&&t!=t}var l=Array.isArray;function d(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=t}(e.length)&&!function(e){var t=p(e)?i.call(e):"";return"[object Function]"==t||"[object GeneratorFunction]"==t}(e)}function p(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}var h,y,v,g=(h=function(e,t){!function(e,t,r){r||(r={});for(var n=-1,o=t.length;++n<o;){var i=t[n];u(r,i,e[i])}}(t,function(e){return d(e)?function(e,t){var r=l(e)||function(e){return function(e){return function(e){return!!e&&"object"==typeof e}(e)&&d(e)}(e)&&o.call(e,"callee")&&(!a.call(e,"callee")||"[object Arguments]"==i.call(e))}(e)?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],n=r.length,c=!!n;for(var u in e)!t&&!o.call(e,u)||c&&("length"==u||s(u,n))||r.push(u);return r}(e,!0):function(e){if(!p(e))return function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}(e);var t,r,i=(r=(t=e)&&t.constructor,t===("function"==typeof r&&r.prototype||n)),a=[];for(var c in e)("constructor"!=c||!i&&o.call(e,c))&&a.push(c);return a}(e)}(t),e)},y=function(e,t){var r=-1,n=t.length,o=n>1?t[n-1]:void 0,i=n>2?t[2]:void 0;for(o=h.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(e,t,r){if(!p(r))return!1;var n=typeof t;return!!("number"==n?d(r)&&s(t,r.length):"string"==n&&t in r)&&f(r[t],e)}(t[0],t[1],i)&&(o=n<3?void 0:o,n=1),e=Object(e);++r<n;){var a=t[r];a&&h(e,a)}return e},v=c(void 0===v?y.length-1:v,0),function(){for(var e=arguments,t=-1,r=c(e.length-v,0),n=Array(r);++t<r;)n[t]=e[v+t];t=-1;for(var o=Array(v+1);++t<v;)o[t]=e[t];return o[v]=n,function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(y,this,o)});e.exports=g},1732:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=u(r(368)),o=r(9529),i=r(7575),a=u(r(3807)),c=r(6183);function u(e){return e&&e.__esModule?e:{default:e}}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,l(n.key),n)}}function l(e){var t=function(e){if("object"!=s(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==s(t)?t:t+""}var d="\nLooks like there is an error in the background page. You might want to inspect your background page for more details.\n",p={channelName:o.DEFAULT_CHANNEL_NAME,state:{},serializer:i.noop,deserializer:i.noop,patchStrategy:a.default},h=function(){return e=function e(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,n=r.channelName,a=void 0===n?p.channelName:n,u=r.state,s=void 0===u?p.state:u,f=r.serializer,l=void 0===f?p.serializer:f,d=r.deserializer,h=void 0===d?p.deserializer:d,y=r.patchStrategy,v=void 0===y?p.patchStrategy:y;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),!a)throw new Error("channelName is required in options");if("function"!=typeof l)throw new Error("serializer must be a function");if("function"!=typeof h)throw new Error("deserializer must be a function");if("function"!=typeof v)throw new Error("patchStrategy must be one of the included patching strategies or a custom patching function");this.channelName=a,this.readyResolved=!1,this.readyPromise=new Promise((function(e){return t.readyResolve=e})),this.browserAPI=(0,c.getBrowserAPI)(),this.initializeStore=this.initializeStore.bind(this),this.browserAPI.runtime.sendMessage({type:o.FETCH_STATE_TYPE,channelName:a},void 0,this.initializeStore),this.deserializer=h,this.serializedPortListener=(0,i.withDeserializer)(h)((function(){var e;return(e=t.browserAPI.runtime.onMessage).addListener.apply(e,arguments)})),this.serializedMessageSender=(0,i.withSerializer)(l)((function(){var e;return(e=t.browserAPI.runtime).sendMessage.apply(e,arguments)})),this.listeners=[],this.state=s,this.patchStrategy=v,this.serializedPortListener((function(e){if(e&&e.channelName===t.channelName)switch(e.type){case o.STATE_TYPE:t.replaceState(e.payload),t.readyResolved||(t.readyResolved=!0,t.readyResolve());break;case o.PATCH_STATE_TYPE:t.patchState(e.payload)}}),(function(e){return Boolean(e)&&"string"==typeof e.type&&e.channelName===t.channelName})),this.dispatch=this.dispatch.bind(this),this.getState=this.getState.bind(this),this.subscribe=this.subscribe.bind(this)},t=[{key:"ready",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!==e?this.readyPromise.then(e):this.readyPromise}},{key:"subscribe",value:function(e){var t=this;return this.listeners.push(e),function(){t.listeners=t.listeners.filter((function(t){return t!==e}))}}},{key:"patchState",value:function(e){this.state=this.patchStrategy(this.state,e),this.listeners.forEach((function(e){return e()}))}},{key:"replaceState",value:function(e){this.state=e,this.listeners.forEach((function(e){return e()}))}},{key:"getState",value:function(){return this.state}},{key:"replaceReducer",value:function(){}},{key:"dispatch",value:function(e){var t=this;return new Promise((function(r,i){t.serializedMessageSender({type:o.DISPATCH_TYPE,channelName:t.channelName,payload:e},null,(function(e){if(e){var o=e.error,a=e.value;if(o){var c=new Error("".concat(d).concat(o));i((0,n.default)(c,o))}else r(a&&a.payload)}else{var u=t.browserAPI.runtime.lastError,s=new Error("".concat(d).concat(u));i((0,n.default)(s,u))}}))}))}},{key:"initializeStore",value:function(e){e&&e.type===o.FETCH_STATE_TYPE&&(this.replaceState(e.payload),this.readyResolved||(this.readyResolved=!0,this.readyResolve()))}}],t&&f(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();t.default=h},3207:(e,t,r)=>{"use strict";Object.defineProperty(t,"nK",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"Iq",{enumerable:!0,get:function(){return n.default}});i(r(1732)),i(r(9449));var n=i(r(6745)),o=i(r(3988));function i(e){return e&&e.__esModule?e:{default:e}}},3807:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=Object.assign({},e);return t.forEach((function(e){var t=e.change,o=e.key,i=e.value;switch(t){case n.DIFF_STATUS_UPDATED:r[o]=i;break;case n.DIFF_STATUS_REMOVED:Reflect.deleteProperty(r,o)}})),r};var n=r(140)},3988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=function(e){return function(){return function(t){return function(r){var n=e[r.type];return t(n?n(r):r)}}}}},6183:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getBrowserAPI=function(){var e;try{e=self.chrome||self.browser||browser}catch(t){e=browser}if(!e)throw new Error("Browser API is not present");return e}},6745:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,o=r(9529),i=r(7575),a=r(6183),c=(n=r(8642))&&n.__esModule?n:{default:n},u=r(8571);function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var f={channelName:o.DEFAULT_CHANNEL_NAME,dispatchResponder:function(e,t){Promise.resolve(e).then((function(e){t({error:null,value:e})})).catch((function(e){console.error("error dispatching result:",e),t({error:e.message,value:null})}))},serializer:i.noop,deserializer:i.noop,diffStrategy:c.default};t.default=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:f).channelName,t=void 0===e?f.channelName:e,r=(0,a.getBrowserAPI)(),n=function(e){return e.type===o.DISPATCH_TYPE&&e.channelName===t},c=(0,u.createDeferredListener)((function(e){return e.type===o.FETCH_STATE_TYPE&&e.channelName===t})),l=(0,u.createDeferredListener)(n);return r.runtime.onMessage.addListener(c.listener),r.runtime.onMessage.addListener(l.listener),function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f,u=a.dispatchResponder,d=void 0===u?f.dispatchResponder:u,p=a.serializer,h=void 0===p?f.serializer:p,y=a.deserializer,v=void 0===y?f.deserializer:y,g=a.diffStrategy,b=void 0===g?f.diffStrategy:g;if("function"!=typeof h)throw new Error("serializer must be a function");if("function"!=typeof v)throw new Error("deserializer must be a function");if("function"!=typeof b)throw new Error("diffStrategy must be one of the included diffing strategies or a custom diff function");var m=(0,i.withSerializer)(h)((function(){for(var e,t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var i=function(){r.runtime.lastError};return(e=r.runtime).sendMessage.apply(e,n.concat([i])),r.tabs.query({},(function(e){var t,o=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw i}}}}(e);try{for(o.s();!(t=o.n()).done;){var a,c=t.value;(a=r.tabs).sendMessage.apply(a,[c.id].concat(n,[i]))}}catch(e){o.e(e)}finally{o.f()}}))})),_=e.getState();e.subscribe((function(){var r=e.getState(),n=b(_,r);n.length&&(_=r,m({type:o.PATCH_STATE_TYPE,payload:n,channelName:t}))})),m({type:o.STATE_TYPE,payload:_,channelName:t}),c.setListener((function(t,r,n){var i=e.getState();n({type:o.FETCH_STATE_TYPE,payload:i})})),(0,i.withDeserializer)(v)(l.setListener)((function(t,r,n){var o=Object.assign({},t.payload,{_sender:r}),i=null;try{i=e.dispatch(o)}catch(e){i=Promise.reject(e.message),console.error(e)}d(i,n)}),n)}}},7575:(e,t)=>{"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,n){return(t=function(e){var t=function(e){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Object.defineProperty(t,"__esModule",{value:!0}),t.withSerializer=t.withDeserializer=t.noop=void 0;var a=t.noop=function(e){return e},c=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;return o(o({},e),e.payload?{payload:t(e.payload)}:{})};t.withDeserializer=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(t){return function(r,n){return t(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a,r=arguments.length>2?arguments[2]:void 0;return r?function(n){for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];return r.apply(void 0,[n].concat(i))?e.apply(void 0,[c(n,t)].concat(i)):e.apply(void 0,[n].concat(i))}:function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];return e.apply(void 0,[c(r,t)].concat(o))}}(r,e,n))}}},t.withSerializer=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a;return function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];if(o.length<=r)throw new Error("Message in request could not be serialized. "+"Expected message in position ".concat(r," but only received ").concat(o.length," args."));return o[r]=c(o[r],e),t.apply(void 0,o)}}}},8571:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createDeferredListener=void 0,t.createDeferredListener=function(e){var t=function(){},r=new Promise((function(e){return t=e}));return{setListener:t,listener:function(t,n,o){if(e(t,n,o))return r.then((function(e){e(t,n,o)})),!0}}}},8642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=[];return Object.keys(t).forEach((function(o){e[o]!==t[o]&&r.push({key:o,value:t[o],change:n.DIFF_STATUS_UPDATED})})),Object.keys(e).forEach((function(e){t.hasOwnProperty(e)||r.push({key:e,change:n.DIFF_STATUS_REMOVED})})),r};var n=r(140)},9448:function(e,t,r){!function(e){"use strict";function t(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function n(e,t){Object.defineProperty(this,"kind",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,"path",{value:t,enumerable:!0})}function o(e,t,r){o.super_.call(this,"E",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function i(e,t){i.super_.call(this,"N",e),Object.defineProperty(this,"rhs",{value:t,enumerable:!0})}function a(e,t){a.super_.call(this,"D",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0})}function c(e,t,r){c.super_.call(this,"A",e),Object.defineProperty(this,"index",{value:t,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function u(e,t,r){var n=e.slice((r||t)+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function s(e){var t=void 0===e?"undefined":A(e);return"object"!==t?t:e===Math?"math":null===e?"null":Array.isArray(e)?"array":"[object Date]"===Object.prototype.toString.call(e)?"date":"function"==typeof e.toString&&/^\/.*\//.test(e.toString())?"regexp":"object"}function f(e,t,r,n,l,d,p){p=p||[];var h=(l=l||[]).slice(0);if(void 0!==d){if(n){if("function"==typeof n&&n(h,d))return;if("object"===(void 0===n?"undefined":A(n))){if(n.prefilter&&n.prefilter(h,d))return;if(n.normalize){var y=n.normalize(h,d,e,t);y&&(e=y[0],t=y[1])}}}h.push(d)}"regexp"===s(e)&&"regexp"===s(t)&&(e=e.toString(),t=t.toString());var v=void 0===e?"undefined":A(e),g=void 0===t?"undefined":A(t),b="undefined"!==v||p&&p[p.length-1].lhs&&p[p.length-1].lhs.hasOwnProperty(d),m="undefined"!==g||p&&p[p.length-1].rhs&&p[p.length-1].rhs.hasOwnProperty(d);if(!b&&m)r(new i(h,t));else if(!m&&b)r(new a(h,e));else if(s(e)!==s(t))r(new o(h,e,t));else if("date"===s(e)&&e-t!==0)r(new o(h,e,t));else if("object"===v&&null!==e&&null!==t)if(p.filter((function(t){return t.lhs===e})).length)e!==t&&r(new o(h,e,t));else{if(p.push({lhs:e,rhs:t}),Array.isArray(e)){var _;for(e.length,_=0;_<e.length;_++)_>=t.length?r(new c(h,_,new a(void 0,e[_]))):f(e[_],t[_],r,n,h,_,p);for(;_<t.length;)r(new c(h,_,new i(void 0,t[_++])))}else{var w=Object.keys(e),S=Object.keys(t);w.forEach((function(o,i){var a=S.indexOf(o);a>=0?(f(e[o],t[o],r,n,h,o,p),S=u(S,a)):f(e[o],void 0,r,n,h,o,p)})),S.forEach((function(e){f(void 0,t[e],r,n,h,e,p)}))}p.length=p.length-1}else e!==t&&("number"===v&&isNaN(e)&&isNaN(t)||r(new o(h,e,t)))}function l(e,t,r,n){return n=n||[],f(e,t,(function(e){e&&n.push(e)}),r),n.length?n:void 0}function d(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":d(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":d(e[t],r.index,r.item);break;case"D":e=u(e,t);break;case"E":case"N":e[t]=r.rhs}return e}function p(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)void 0===n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":d(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function h(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":h(o[r.path[n]],r.index,r.item);break;case"D":case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":h(e[t],r.index,r.item);break;case"D":case"E":e[t]=r.lhs;break;case"N":e=u(e,t)}return e}function y(e){return"color: "+j[e].color+"; font-weight: bold"}function v(e,t,r,n){var o=l(e,t);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(e){r.log("diff")}o?o.forEach((function(e){var t=e.kind,n=function(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}(e);r.log.apply(r,["%c "+j[t].text,y(t)].concat(T(n)))})):r.log("—— no diff ——");try{r.groupEnd()}catch(e){r.log("—— diff end —— ")}}function g(e,t,r,n){switch(void 0===e?"undefined":A(e)){case"object":return"function"==typeof e[n]?e[n].apply(e,T(r)):e[n];case"function":return e(t);default:return e}}function b(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?function(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=["action"];return i.push("%c"+String(e.type)),t&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}(t):o,a=t.collapsed,c=t.colors,u=t.level,s=t.diff,f=void 0===t.titleFormatter;e.forEach((function(o,l){var d=o.started,p=o.startedTime,h=o.action,y=o.prevState,b=o.error,m=o.took,_=o.nextState,w=e[l+1];w&&(_=w.prevState,m=w.started-d);var S=n(h),P="function"==typeof a?a((function(){return _}),h,o):a,A=E(p),T=c.title?"color: "+c.title(S)+";":"",O=["color: gray; font-weight: lighter;"];O.push(T),t.timestamp&&O.push("color: gray; font-weight: lighter;"),t.duration&&O.push("color: gray; font-weight: lighter;");var j=i(S,A,m);try{P?c.title&&f?r.groupCollapsed.apply(r,["%c "+j].concat(O)):r.groupCollapsed(j):c.title&&f?r.group.apply(r,["%c "+j].concat(O)):r.group(j)}catch(e){r.log(j)}var D=g(u,S,[y],"prevState"),N=g(u,S,[S],"action"),C=g(u,S,[b,y],"error"),k=g(u,S,[_],"nextState");if(D)if(c.prevState){var x="color: "+c.prevState(y)+"; font-weight: bold";r[D]("%c prev state",x,y)}else r[D]("prev state",y);if(N)if(c.action){var M="color: "+c.action(S)+"; font-weight: bold";r[N]("%c action    ",M,S)}else r[N]("action    ",S);if(b&&C)if(c.error){var F="color: "+c.error(b,y)+"; font-weight: bold;";r[C]("%c error     ",F,b)}else r[C]("error     ",b);if(k)if(c.nextState){var z="color: "+c.nextState(_)+"; font-weight: bold";r[k]("%c next state",z,_)}else r[k]("next state",_);s&&v(y,_,r,P);try{r.groupEnd()}catch(e){r.log("—— log end ——")}}))}function m(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},D,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,c=t.diffPredicate;if(void 0===r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return console.error("[redux-logger] redux-logger not installed. Make sure to pass logger instance as middleware:\n// Logger with default options\nimport { logger } from 'redux-logger'\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n// Or you can create your own logger with custom options http://bit.ly/redux-logger-options\nimport createLogger from 'redux-logger'\nconst logger = createLogger({\n  // ...options\n});\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n"),function(){return function(e){return function(t){return e(t)}}};var u=[];return function(e){var r=e.getState;return function(e){return function(s){if("function"==typeof i&&!i(r,s))return e(s);var f={};u.push(f),f.started=P.now(),f.startedTime=new Date,f.prevState=n(r()),f.action=s;var l=void 0;if(a)try{l=e(s)}catch(e){f.error=o(e)}else l=e(s);f.took=P.now()-f.started,f.nextState=n(r());var d=t.diff&&"function"==typeof c?c(r,s):t.diff;if(b(u,Object.assign({},t,{diff:d})),u.length=0,f.error)throw f.error;return l}}}}var _,w,S=function(e,t){return function(e,t){return new Array(t+1).join(e)}("0",t-e.toString().length)+e},E=function(e){return S(e.getHours(),2)+":"+S(e.getMinutes(),2)+":"+S(e.getSeconds(),2)+"."+S(e.getMilliseconds(),3)},P="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},O=[];_="object"===(void 0===r.g?"undefined":A(r.g))&&r.g?r.g:"undefined"!=typeof window?window:{},(w=_.DeepDiff)&&O.push((function(){void 0!==w&&_.DeepDiff===l&&(_.DeepDiff=w,w=void 0)})),t(o,n),t(i,n),t(a,n),t(c,n),Object.defineProperties(l,{diff:{value:l,enumerable:!0},observableDiff:{value:f,enumerable:!0},applyDiff:{value:function(e,t,r){e&&t&&f(e,t,(function(n){r&&!r(e,t,n)||p(e,t,n)}))},enumerable:!0},applyChange:{value:p,enumerable:!0},revertChange:{value:function(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)void 0===i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":h(i[r.path[n]],r.index,r.item);break;case"D":case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}},enumerable:!0},isConflict:{value:function(){return void 0!==w},enumerable:!0},noConflict:{value:function(){return O&&(O.forEach((function(e){e()})),O=null),l},enumerable:!0}});var j={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},D={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},N=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return"function"==typeof t||"function"==typeof r?m()({dispatch:t,getState:r}):void console.error("\n[redux-logger v3] BREAKING CHANGE\n[redux-logger v3] Since 3.0.0 redux-logger exports by default logger with default settings.\n[redux-logger v3] Change\n[redux-logger v3] import createLogger from 'redux-logger'\n[redux-logger v3] to\n[redux-logger v3] import { createLogger } from 'redux-logger'\n")};e.defaults=D,e.createLogger=m,e.logger=N,e.default=N,Object.defineProperty(e,"__esModule",{value:!0})}(t)},9449:(e,t)=>{"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){for(var t=arguments.length,o=new Array(t>1?t-1:0),i=1;i<t;i++)o[i-1]=arguments[i];var a,c=function(){throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")},u={getState:e.getState.bind(e),dispatch:function(){return c.apply(void 0,arguments)}};return o=(o||[]).map((function(e){return e(u)})),c=n.apply(void 0,function(e){if(Array.isArray(e))return r(e)}(a=o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(a)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())(e.dispatch),e.dispatch=c,e}},9529:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.STATE_TYPE=t.PATCH_STATE_TYPE=t.FETCH_STATE_TYPE=t.DISPATCH_TYPE=t.DEFAULT_CHANNEL_NAME=void 0,t.DISPATCH_TYPE="webext.dispatch",t.FETCH_STATE_TYPE="webext.fetch_state",t.STATE_TYPE="webext.state",t.PATCH_STATE_TYPE="webext.patch_state",t.DEFAULT_CHANNEL_NAME="webext.channel"}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),(()=>{"use strict";function e(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var t=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),n=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${n()}`,REPLACE:`@@redux/REPLACE${n()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${n()}`};function i(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function a(r,n,c){if("function"!=typeof r)throw new Error(e(2));if("function"==typeof n&&"function"==typeof c||"function"==typeof c&&"function"==typeof arguments[3])throw new Error(e(0));if("function"==typeof n&&void 0===c&&(c=n,n=void 0),void 0!==c){if("function"!=typeof c)throw new Error(e(1));return c(a)(r,n)}let u=r,s=n,f=new Map,l=f,d=0,p=!1;function h(){l===f&&(l=new Map,f.forEach(((e,t)=>{l.set(t,e)})))}function y(){if(p)throw new Error(e(3));return s}function v(t){if("function"!=typeof t)throw new Error(e(4));if(p)throw new Error(e(5));let r=!0;h();const n=d++;return l.set(n,t),function(){if(r){if(p)throw new Error(e(6));r=!1,h(),l.delete(n),f=null}}}function g(t){if(!i(t))throw new Error(e(7));if(void 0===t.type)throw new Error(e(8));if("string"!=typeof t.type)throw new Error(e(17));if(p)throw new Error(e(9));try{p=!0,s=u(s,t)}finally{p=!1}return(f=l).forEach((e=>{e()})),t}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(t){if("function"!=typeof t)throw new Error(e(10));u=t,g({type:o.REPLACE})},[t]:function(){const r=v;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(e(11));function n(){const e=t;e.next&&e.next(y())}return n(),{unsubscribe:r(n)}},[t](){return this}}}}}function c(t){const r=Object.keys(t),n={};for(let e=0;e<r.length;e++){const o=r[e];"function"==typeof t[o]&&(n[o]=t[o])}const i=Object.keys(n);let a;try{!function(t){Object.keys(t).forEach((r=>{const n=t[r];if(void 0===n(void 0,{type:o.INIT}))throw new Error(e(12));if(void 0===n(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw new Error(e(13))}))}(n)}catch(e){a=e}return function(t={},r){if(a)throw a;let o=!1;const c={};for(let a=0;a<i.length;a++){const u=i[a],s=n[u],f=t[u],l=s(f,r);if(void 0===l)throw r&&r.type,new Error(e(14));c[u]=l,o=o||l!==f}return o=o||i.length!==Object.keys(t).length,o?c:t}}function u(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce(((e,t)=>(...r)=>e(t(...r))))}function s(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var f=s(),l=s,d=Symbol.for("immer-nothing"),p=Symbol.for("immer-draftable"),h=Symbol.for("immer-state");function y(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var v=Object.getPrototypeOf;function g(e){return!!e&&!!e[h]}function b(e){return!!e&&(_(e)||Array.isArray(e)||!!e[p]||!!e.constructor?.[p]||A(e)||T(e))}var m=Object.prototype.constructor.toString();function _(e){if(!e||"object"!=typeof e)return!1;const t=v(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===m}function w(e,t){0===S(e)?Reflect.ownKeys(e).forEach((r=>{t(r,e[r],e)})):e.forEach(((r,n)=>t(n,r,e)))}function S(e){const t=e[h];return t?t.type_:Array.isArray(e)?1:A(e)?2:T(e)?3:0}function E(e,t){return 2===S(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function P(e,t,r){const n=S(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function A(e){return e instanceof Map}function T(e){return e instanceof Set}function O(e){return e.copy_||e.base_}function j(e,t){if(A(e))return new Map(e);if(T(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=_(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[h];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(v(e),t)}{const t=v(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function D(e,t=!1){return C(e)||g(e)||!b(e)||(S(e)>1&&(e.set=e.add=e.clear=e.delete=N),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>D(t,!0)))),e}function N(){y(2)}function C(e){return Object.isFrozen(e)}var k,x={};function M(e){const t=x[e];return t||y(0),t}function F(){return k}function z(e,t){t&&(M("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function R(e){I(e),e.drafts_.forEach(U),e.drafts_=null}function I(e){e===k&&(k=e.parent_)}function L(e){return k={drafts_:[],parent_:k,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function U(e){const t=e[h];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function Y(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[h].modified_&&(R(t),y(4)),b(e)&&(e=H(t,e),t.parent_||K(t,e)),t.patches_&&M("Patches").generateReplacementPatches_(r[h].base_,e,t.patches_,t.inversePatches_)):e=H(t,r,[]),R(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==d?e:void 0}function H(e,t,r){if(C(t))return t;const n=t[h];if(!n)return w(t,((o,i)=>B(e,n,t,o,i,r))),t;if(n.scope_!==e)return t;if(!n.modified_)return K(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),w(o,((o,a)=>B(e,n,t,o,a,r,i))),K(e,t,!1),r&&e.patches_&&M("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function B(e,t,r,n,o,i,a){if(g(o)){const a=H(e,o,i&&t&&3!==t.type_&&!E(t.assigned_,n)?i.concat(n):void 0);if(P(r,n,a),!g(a))return;e.canAutoFreeze_=!1}else a&&r.add(o);if(b(o)&&!C(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;H(e,o),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||K(e,o)}}function K(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&D(t,r)}var $={get(e,t){if(t===h)return e;const r=O(e);if(!E(r,t))return function(e,t,r){const n=V(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!b(n)?n:n===q(e.base_,t)?(G(e),e.copy_[t]=J(n,e)):n},has:(e,t)=>t in O(e),ownKeys:e=>Reflect.ownKeys(O(e)),set(e,t,r){const n=V(O(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=q(O(e),t),a=n?.[h];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((o=r)===(i=n)?0!==o||1/o==1/i:o!=o&&i!=i)&&(void 0!==r||E(e.base_,t)))return!0;G(e),X(e)}var o,i;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==q(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,G(e),X(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=O(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){y(11)},getPrototypeOf:e=>v(e.base_),setPrototypeOf(){y(12)}},W={};function q(e,t){const r=e[h];return(r?O(r):e)[t]}function V(e,t){if(!(t in e))return;let r=v(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=v(r)}}function X(e){e.modified_||(e.modified_=!0,e.parent_&&X(e.parent_))}function G(e){e.copy_||(e.copy_=j(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function J(e,t){const r=A(e)?M("MapSet").proxyMap_(e,t):T(e)?M("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:F(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=$;r&&(o=[n],i=W);const{revoke:a,proxy:c}=Proxy.revocable(o,i);return n.draft_=c,n.revoke_=a,c}(e,t);return(t?t.scope_:F()).drafts_.push(r),r}function Q(e){if(!b(e)||C(e))return e;const t=e[h];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=j(e,t.scope_.immer_.useStrictShallowCopy_)}else r=j(e,!0);return w(r,((e,t)=>{P(r,e,Q(t))})),t&&(t.finalized_=!1),r}w($,((e,t)=>{W[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),W.deleteProperty=function(e,t){return W.set.call(this,e,t,void 0)},W.set=function(e,t,r){return $.set.call(this,e[0],t,r,e[0])};var Z=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...o){return n.produce(e,(e=>t.call(this,e,...o)))}}let n;if("function"!=typeof t&&y(6),void 0!==r&&"function"!=typeof r&&y(7),b(e)){const o=L(this),i=J(e,void 0);let a=!0;try{n=t(i),a=!1}finally{a?R(o):I(o)}return z(o,r),Y(n,o)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===d&&(n=void 0),this.autoFreeze_&&D(n,!0),r){const t=[],o=[];M("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}y(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,(t=>e(t,...r)));let r,n;return[this.produce(e,t,((e,t)=>{r=e,n=t})),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;b(e)||y(8),g(e)&&(g(t=e)||y(10),e=Q(t));const r=L(this),n=J(e,void 0);return n[h].isManual_=!0,I(r),n}finishDraft(e,t){const r=e&&e[h];r&&r.isManual_||y(9);const{scope_:n}=r;return z(n,t),Y(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=M("Patches").applyPatches_;return g(e)?n(e,t):this.produce(e,(e=>n(e,t)))}},ee=Z.produce;Z.produceWithPatches.bind(Z),Z.setAutoFreeze.bind(Z),Z.setUseStrictShallowCopy.bind(Z),Z.applyPatches.bind(Z),Z.createDraft.bind(Z),Z.finishDraft.bind(Z);var te="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?u:u.apply(null,arguments)};function re(e,t){function r(...r){if(t){let n=t(...r);if(!n)throw new Error(ve(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:r[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>function(e){return i(e)&&"type"in e&&"string"==typeof e.type}(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var ne=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function oe(e){return b(e)?ee(e,(()=>{})):e}function ie(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var ae=e=>t=>{setTimeout(t,e)};function ce(t){const r=function(e){const{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{};let i=new ne;return t&&("boolean"==typeof t?i.push(f):i.push(l(t.extraArgument))),i},{reducer:n,middleware:o,devTools:s=!0,duplicateMiddlewareCheck:d=!0,preloadedState:p,enhancers:h}=t||{};let y,v;if("function"==typeof n)y=n;else{if(!i(n))throw new Error(ve(1));y=c(n)}v="function"==typeof o?o(r):r();let g=u;s&&(g=te({trace:!1,..."object"==typeof s&&s}));const b=function(...t){return r=>(n,o)=>{const i=r(n,o);let a=()=>{throw new Error(e(15))};const c={getState:i.getState,dispatch:(e,...t)=>a(e,...t)},s=t.map((e=>e(c)));return a=u(...s)(i.dispatch),{...i,dispatch:a}}}(...v),m=(e=>function(t){const{autoBatch:r=!0}=t??{};let n=new ne(e);return r&&n.push(((e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,a=!1;const c=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:ae(10):"callback"===e.type?e.queueNotification:ae(e.timeout),s=()=>{a=!1,i&&(i=!1,c.forEach((e=>e())))};return Object.assign({},n,{subscribe(e){const t=n.subscribe((()=>o&&e()));return c.add(e),()=>{t(),c.delete(e)}},dispatch(e){try{return o=!e?.meta?.RTK_autoBatch,i=!o,i&&(a||(a=!0,u(s))),n.dispatch(e)}finally{o=!0}}})})("object"==typeof r?r:void 0)),n})(b);return a(y,p,g(..."function"==typeof h?h(m):m()))}function ue(e){const t={},r=[];let n;const o={addCase(e,r){const n="string"==typeof e?e:e.type;if(!n)throw new Error(ve(28));if(n in t)throw new Error(ve(29));return t[n]=r,o},addMatcher:(e,t)=>(r.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(n=e,o)};return e(o),[t,r,n]}var se=Symbol.for("rtk-slice-createasyncthunk");function fe(e,t){return`${e}/${t}`}function le({creators:e}={}){const t=e?.asyncThunk?.[se];return function(e){const{name:r,reducerPath:n=r}=e;if(!r)throw new Error(ve(11));const o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(o),a={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},c={addCase(e,t){const r="string"==typeof e?e:e.type;if(!r)throw new Error(ve(12));if(r in a.sliceCaseReducersByType)throw new Error(ve(13));return a.sliceCaseReducersByType[r]=t,c},addMatcher:(e,t)=>(a.sliceMatchers.push({matcher:e,reducer:t}),c),exposeAction:(e,t)=>(a.actionCreators[e]=t,c),exposeCaseReducer:(e,t)=>(a.sliceCaseReducersByName[e]=t,c)};function u(){const[t={},r=[],n]="function"==typeof e.extraReducers?ue(e.extraReducers):[e.extraReducers],o={...t,...a.sliceCaseReducersByType};return function(e){let t,[i,c,u]=ue((e=>{for(let t in o)e.addCase(t,o[t]);for(let t of a.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)}));if("function"==typeof e)t=()=>oe(e());else{const r=oe(e);t=()=>r}function s(e=t(),r){let n=[i[r.type],...c.filter((({matcher:e})=>e(r))).map((({reducer:e})=>e))];return 0===n.filter((e=>!!e)).length&&(n=[u]),n.reduce(((e,t)=>{if(t){if(g(e)){const n=t(e,r);return void 0===n?e:n}if(b(e))return ee(e,(e=>t(e,r)));{const n=t(e,r);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e}),e)}return s.getInitialState=t,s}(e.initialState)}i.forEach((n=>{const i=o[n],a={reducerName:n,type:fe(r,n),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(i)?function({type:e,reducerName:t,createNotation:r},n,o){let i,a;if("reducer"in n){if(r&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(n))throw new Error(ve(17));i=n.reducer,a=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,a?re(e,a):re(e))}(a,i,c):function({type:e,reducerName:t},r,n,o){if(!o)throw new Error(ve(18));const{payloadCreator:i,fulfilled:a,pending:c,rejected:u,settled:s,options:f}=r,l=o(e,i,f);n.exposeAction(t,l),a&&n.addCase(l.fulfilled,a),c&&n.addCase(l.pending,c),u&&n.addCase(l.rejected,u),s&&n.addMatcher(l.settled,s),n.exposeCaseReducer(t,{fulfilled:a||he,pending:c||he,rejected:u||he,settled:s||he})}(a,i,c,t)}));const s=e=>e,f=new Map,l=new WeakMap;let d;function p(e,t){return d||(d=u()),d(e,t)}function h(){return d||(d=u()),d.getInitialState()}function y(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=ie(l,n,h)),o}function o(t=s){const n=ie(f,r,(()=>new WeakMap));return ie(n,t,(()=>{const n={};for(const[o,i]of Object.entries(e.selectors??{}))n[o]=de(i,t,(()=>ie(l,t,h)),r);return n}))}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}const v={name:r,reducer:p,actions:a.actionCreators,caseReducers:a.sliceCaseReducersByName,getInitialState:h,...y(n),injectInto(e,{reducerPath:t,...r}={}){const o=t??n;return e.inject({reducerPath:o,reducer:p},r),{...v,...y(o,!0)}}};return v}}function de(e,t,r,n){function o(o,...i){let a=t(o);return void 0===a&&n&&(a=r()),e(a,...i)}return o.unwrapped=e,o}var pe=le();function he(){}var{assign:ye}=Object;function ve(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original");var ge=r(9448),be=r(3207);const me={isLoaded:!1},_e=pe({name:"content",initialState:me,reducers:{reset:()=>me,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:we,reducer:Se}=_e;var Ee,Pe;!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(Ee||(Ee={})),function(e){e.Default="default"}(Pe||(Pe={}));const Ae={isOpen:!1},Te=pe({name:"sidePanel",initialState:Ae,reducers:{reset:()=>Ae}}),{actions:Oe,reducer:je}=Te,De={},Ne=((0,be.nK)(De),ge.logger,(0,be.nK)(De),ge.logger,[(0,be.nK)(De),ge.logger]);var Ce;!function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(Ce||(Ce={})),(({channelName:e}={})=>{const t=ce({devTools:!0,reducer:c({sidePanel:je,content:Se}),middleware:e=>e().concat(Ne)});e&&(0,be.Iq)({channelName:e})(t)})({channelName:Ce.ContentPort}),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!0}).catch(console.error)})()})();