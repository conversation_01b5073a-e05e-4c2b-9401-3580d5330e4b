import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useConfig } from './ConfigContext';

export const ThemeToggle = () => {
  const { config, updateConfig } = useConfig();
  const currentTheme = config?.theme || 'light';
  const isDark = currentTheme === 'dark';

  const toggleTheme = () => {
    const newTheme = isDark ? 'light' : 'dark';
    updateConfig({ theme: newTheme });
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
          variant="ghost"
          size="sm"
          className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center transition-all duration-200"
          onClick={toggleTheme}
        >
          <span className="text-base transition-transform duration-200 hover:scale-110">
            {isDark ? '☀️' : '🌙'}
          </span>
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
        Switch to {isDark ? 'light' : 'dark'} theme
      </TooltipContent>
    </Tooltip>
  );
};
