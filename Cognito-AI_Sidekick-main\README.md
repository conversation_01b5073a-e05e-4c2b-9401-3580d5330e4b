# ChromePanion

A private, local AI-powered web search assistant that connects exclusively to local AI models (Ollama) to analyze web search results through specialized AI personas.

## Features

- **Private AI Search:** Local processing via Ollama - no external AI services
- **Google Search Integration:** Fetches and analyzes web search results
- **10 AI Personas:** <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, In<PERSON>ti<PERSON>or, <PERSON><PERSON>matist, Enthus<PERSON>t, <PERSON><PERSON><PERSON>, Friend
- **Note Taking:** Save important information within the side panel
- **Chat History:** Track your interactions

## Installation

### From Source
1. Clone the repository and install dependencies:
   ```bash
   git clone https://github.com/user/ChromePanion.git
   cd ChromePanion
   npm install
   npm start
   ```
2. Open Chrome → `chrome://extensions` → Enable Developer mode
3. Click "Load unpacked" and select the `dist/chrome` folder

## Usage

1. Install and run Ollama locally (`http://localhost:11434`)
2. Configure Ollama connection in ChromePanion settings
3. Select an AI persona for your search style
4. Type your search query and get AI-analyzed results
