@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Apple Design System - Light Mode */
  --background: #ffffff;
  --foreground: #1d1d1f;
  --card: #ffffff;
  --card-foreground: #1d1d1f;
  --popover: #ffffff;
  --popover-foreground: #1d1d1f;
  --primary: #007aff;
  --primary-foreground: #ffffff;
  --secondary: #f2f2f7;
  --secondary-foreground: #1d1d1f;
  --muted: #f2f2f7;
  --muted-foreground: #8e8e93;
  --accent: #007aff;
  --accent-foreground: #ffffff;
  --destructive: #ff3b30;
  --destructive-foreground: #ffffff;
  --border: #d1d1d6;
  --input: #f2f2f7;
  --ring: #007aff;

  /* Apple spacing scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 0.75rem;   /* 12px */
  --spacing-lg: 1rem;      /* 16px */
  --spacing-xl: 1.5rem;    /* 24px */
  --spacing-2xl: 2rem;     /* 32px */
  --spacing-3xl: 3rem;     /* 48px */

  /* Apple border radius scale */
  --radius-sm: 0.375rem;   /* 6px */
  --radius: 0.5rem;        /* 8px */
  --radius-md: 0.75rem;    /* 12px */
  --radius-lg: 1rem;       /* 16px */
  --radius-xl: 1.25rem;    /* 20px */

  /* Apple shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Legacy compatibility */
  --chart-1: #007aff;
  --chart-2: #34c759;
  --chart-3: #ff9500;
  --chart-4: #af52de;
  --chart-5: #ff2d92;
  --sidebar: #f2f2f7;
  --sidebar-foreground: #1d1d1f;
  --sidebar-primary: #007aff;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f2f2f7;
  --sidebar-accent-foreground: #1d1d1f;
  --sidebar-border: #d1d1d6;
  --sidebar-ring: #007aff;
  --input-background: rgba(242, 242, 247, 0.8);

  /* Markdown styling */
  --markdown-inline-code-foreground: #1d1d1f;
  --markdown-code-background: #f2f2f7;
  --markdown-pre-foreground: #ffffff;
  --markdown-pre-background: #1d1d1f;
  --markdown-thead-background: var(--secondary);
  --markdown-thead-foreground: var(--secondary-foreground);
  --markdown-link: var(--primary);
  --markdown-strong: var(--foreground);
  --markdown-em: var(--foreground);

  /* Select component variables for light mode */
  --bg: #ffffff;
  --text: #1d1d1f;
  --active: #007aff;
  }

.dark {
  /* Apple Design System - Dark Mode */
  --background: #000000;
  --foreground: #ffffff;
  --card: #1c1c1e;
  --card-foreground: #ffffff;
  --popover: #1c1c1e;
  --popover-foreground: #ffffff;
  --primary: #0a84ff;
  --primary-foreground: #ffffff;
  --secondary: #2c2c2e;
  --secondary-foreground: #ffffff;
  --muted: #2c2c2e;
  --muted-foreground: #8e8e93;
  --accent: #0a84ff;
  --accent-foreground: #ffffff;
  --destructive: #ff453a;
  --destructive-foreground: #ffffff;
  --border: #38383a;
  --input: #2c2c2e;
  --ring: #0a84ff;

  /* Apple shadows for dark mode */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);

  /* Legacy compatibility */
  --chart-1: #0a84ff;
  --chart-2: #30d158;
  --chart-3: #ff9f0a;
  --chart-4: #bf5af2;
  --chart-5: #ff375f;
  --sidebar: #1c1c1e;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #0a84ff;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #2c2c2e;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #38383a;
  --sidebar-ring: #0a84ff;
  --input-background: rgba(44, 44, 46, 0.8);

  /* Markdown styling for dark mode */
  --markdown-inline-code-foreground: #ffffff;
  --markdown-code-background: #2c2c2e;
  --markdown-pre-foreground: #000000;
  --markdown-pre-background: #ffffff;
  --markdown-thead-background: var(--secondary);
  --markdown-thead-foreground: var(--secondary-foreground);
  --markdown-link: var(--primary);
  --markdown-strong: var(--foreground);
  --markdown-em: var(--foreground);

  /* Select component variables for dark mode */
  --bg: #1c1c1e;
  --text: #ffffff;
  --active: #0a84ff;
  }

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@theme {
  --tracking-1: 0em;
  --tracking-2: 0.025em;
  --tracking-3: 0.05em;
  --tracking-4: 0.1em;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html,
  body {
    @apply bg-background text-foreground;
    @apply no-scrollbar;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-feature-settings: 'kern' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Apple Typography Scale */
  .text-apple-title1 {
    font-size: 2.125rem; /* 34px */
    line-height: 1.2;
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  .text-apple-title2 {
    font-size: 1.75rem; /* 28px */
    line-height: 1.25;
    font-weight: 700;
    letter-spacing: -0.01em;
  }

  .text-apple-title3 {
    font-size: 1.375rem; /* 22px */
    line-height: 1.3;
    font-weight: 600;
    letter-spacing: -0.01em;
  }

  .text-apple-headline {
    font-size: 1.0625rem; /* 17px */
    line-height: 1.35;
    font-weight: 600;
    letter-spacing: -0.005em;
  }

  .text-apple-body {
    font-size: 1.0625rem; /* 17px */
    line-height: 1.35;
    font-weight: 400;
    letter-spacing: -0.005em;
  }

  .text-apple-callout {
    font-size: 1rem; /* 16px */
    line-height: 1.4;
    font-weight: 400;
    letter-spacing: -0.005em;
  }

  .text-apple-subheadline {
    font-size: 0.9375rem; /* 15px */
    line-height: 1.4;
    font-weight: 400;
    letter-spacing: -0.005em;
  }

  .text-apple-footnote {
    font-size: 0.8125rem; /* 13px */
    line-height: 1.4;
    font-weight: 400;
    letter-spacing: -0.005em;
  }

  .text-apple-caption1 {
    font-size: 0.75rem; /* 12px */
    line-height: 1.35;
    font-weight: 400;
    letter-spacing: 0;
  }

  .text-apple-caption2 {
    font-size: 0.6875rem; /* 11px */
    line-height: 1.35;
    font-weight: 400;
    letter-spacing: 0.01em;
  }
}

@utility no-scrollbar {
  @apply [scrollbar-width:none] [&::-webkit-scrollbar]:hidden;
}

@utility scrollbar-hidden {
  &::-webkit-scrollbar {
    display: none;
  }
}

/* For Webkit-based browsers (Chrome, Safari and Opera) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* For IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

:root {
--markdown-h1: var(--foreground);
--markdown-h2: var(--foreground);
--markdown-h3: var(--foreground);
--markdown-table-border: var(--border);
}

code, pre {
    font-family: monospace;
}


.markdown-body h1 {
color: var(--markdown-h1);
font-size: 1.5rem;
font-weight: 800;
margin: 1rem 0 1rem;
border-bottom: 2px solid var(--markdown-h1);
padding-bottom: 0.5rem;
}

.markdown-body h2 {
color: var(--markdown-h2);
font-size: 1.25rem;
font-weight: 700;
margin: 1rem 0 0.75rem;
border-bottom: 1px solid var(--markdown-h2);
padding-bottom: 0.4rem;
}

.markdown-body h3 {
color: var(--markdown-h3);
font-size: 1.1rem;
font-weight: 600;
margin: 0.75rem 0 0.5rem;
border-bottom: 1px dashed var(--markdown-h3);
padding-bottom: 0.3rem;
}

.markdown-body strong {
color: var(--markdown-strong);
font-weight: 700;
font-family: 'Poppins', sans-serif;
}

.markdown-body em {
color: var(--markdown-em);
font-style: italic;
}

.markdown-body a {
color: var(--markdown-link);
text-decoration: underline;
padding: 2px 7px;
border-radius: 6px;
}

.markdown-body ul,
.markdown-body ol {
padding-left: 2rem; /* Increased indentation */
padding-top: 0.5rem;
padding-bottom: 0.5rem;
}

.markdown-body ul {
list-style-type: disc;
}

.markdown-body ol {
list-style-type: decimal;
}

.markdown-body p {
padding-top: 0;
padding-bottom: 0.2rem;
word-break: break-word;
overflow-wrap: break-word;
white-space: pre-wrap;
}

.markdown-body pre {
  overflow-x: auto;
  padding: 1rem;
  margin: 0 0;
  background: var(--markdown-pre-background);
  color: var(--markdown-pre-foreground);
  border-radius: 4px;
  max-width: 100%;
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-all;
  display: block;
  text-indent: 0;

}pre > code {
    display: block;
    text-indent: 0;
    color: inherit;
    white-space: inherit;
}

/* Customizing scrollbar within elements (override by the previous setting */
.markdown-body pre::-webkit-scrollbar {
    width: 8px;
}

.markdown-body pre::-webkit-scrollbar-thumb {
    background-color: rgba(128, 128, 128, 0.5); /* Color of the scrollbar thumb */
    border-radius: 4px;
}

.markdown-body pre::-webkit-scrollbar-track {
    background-color: transparent;
}

.markdown-body pre {
    scrollbar-width: thin;
    scrollbar-color: rgba(128, 128, 128, 0.5) transparent; /* Thumb and track color for Firefox */
}


.markdown-body code {
color: var(--markdown-inline-code-foreground);
background: var(--markdown-code-background);
padding: 0.2rem 0.4rem;
border-radius: 4px;
font-family: monospace;
word-wrap: break-word;
text-indent: 0;
}

/* Wrapper for tables to handle overflow */
.markdown-table-wrapper {
  overflow-x: auto; /* Enable horizontal scrolling for the table */
  margin: 1rem 0;   /* Apply vertical margins here, moved from table */
  max-width: 100%;  /* Ensure the wrapper itself respects the container width */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(128, 128, 128, 0.5) transparent; /* Firefox */
}

.markdown-table-wrapper::-webkit-scrollbar {
  height: 8px; /* For horizontal scrollbar */
  width: 8px;  /* For vertical scrollbar (if ever needed by content) */
}

.markdown-table-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(128, 128, 128, 0.5);
  border-radius: 4px;
}

.markdown-table-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}

/* Table styles */
.markdown-body table {
border: 2px solid var(--markdown-table-border);
border-collapse: collapse;
width: 100%;
}

.markdown-body thead {
background: var(--markdown-thead-background);
border-bottom: 2px solid var(--markdown-table-border);
color: var(--markdown-thead-foreground);
}

.markdown-body th,
.markdown-body td {
padding: 0.5rem;
border: 1px solid var(--markdown-table-border);
}

.markdown-body tr:hover {
background: rgba(0,0,0,0.05);
}

/* Blockquote */
.markdown-body blockquote {
border-left: 4px solid var(--markdown-h2);
margin: 1em 0;
padding: 0.5em 1em;
background: rgba(0,0,0,0.03);
color: var(--markdown-h2);
}

/* Horizontal rule */
.markdown-body hr {
border: none;
border-top: 1px solid var(--markdown-h2);
margin: 1.5em 0;
}

/* Subscript and superscript */
.markdown-body sub,
.markdown-body sup {
font-size: 0.8em;
line-height: 0;
position: relative;
vertical-align: baseline;
}

.markdown-body sup {
top: -0.5em;
}

.markdown-body sub {
bottom: -0.2em;
}



/* Responsive tweaks */
@media (max-width: 600px) {
.markdown-body pre {
  max-width: 98vw;
  font-size: 0.95em;
}
.markdown-body table {
  font-size: 0.95em;
}
}



/* --- Chat Message Styles --- */

/* Clean message display without bubbles */
.chatMessage {
  transition: background-color 0.15s ease-in-out;
}

.chatMessage:hover {
  background-color: var(--muted);
  border-radius: var(--radius-md);
}

/* Ensure proper spacing for clean message layout */
.message-markdown {
  line-height: 1.6;
}

.message-markdown p:first-child {
  margin-top: 0;
}

.message-markdown p:last-child {
  margin-bottom: 0;
}

/* --- Global Paper Texture Styles --- */

html[data-paper-texture="true"] .chatMessage::before {
  content: "";
  position: absolute;
  width: 100%; height: 100%;
  top: 0; left: 0; right: 0; bottom: 0;
  background-image: url('~assets/images/paper-texture.png');
  background-size: auto;
  background-repeat: repeat;
  opacity: 0.3;
  pointer-events: none;
  mix-blend-mode: multiply;
  filter: contrast(1) brightness(1);
  z-index: 1;
  border-radius: inherit;
}

html[data-paper-texture="true"] .settings-drawer-content::before {
  content: "";
  position: absolute; 
  width: 100%; height: 100%;
  top: 0; left: 0; right: 0; bottom: 0;
  background-image: url('~assets/images/paper-texture.png');
  background-size: auto;
  background-repeat: repeat;
  opacity: 0.3;
  pointer-events: none;
  mix-blend-mode: multiply;
  filter: contrast(1) brightness(1);
  z-index: 1; 
  border-radius: inherit; 
}

html[data-paper-texture="true"] #root::before {
  content: "";
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  width: 100%; height: 100%;
  background-image: url('~assets/images/paper-texture.png');
  background-size: 512px;
  background-repeat: repeat;
  opacity: 0.3;
  pointer-events: none;
  mix-blend-mode: multiply; 
  filter: contrast(1) brightness(1);
  z-index: 0;
  border-radius: inherit;
}

.focus-visible\:ring-1.focus-visible\:ring-offset-0.focus-visible.AutoResizeTextarea {
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1); /* Adjust color and intensity */
}

.fingerprint-pulse-btn {
  position: relative;
  padding: 0.5rem;
  border-radius: 50%;
  border: 0px solid var(--active);
  background: var(--bg, #fff);
  overflow: visible;
  box-shadow: 0 0 0 0 var(--active);
  animation: fingerprint-shadow-pulse 2s infinite cubic-bezier(.66,0,0,1);
  transition: border-color 0.5s;
}

@keyframes fingerprint-shadow-pulse {
  0% {
    box-shadow: 0 0 0 0 var(--active);
  }
  70% {
    box-shadow: 0 0 0 12px rgba(102,215,238,0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(102,215,238,0);
  }
}

:root {
  --input-base-shadow: 0 4px 24px 0 rgba(0,0,0,0.12);
  --input-anim-static-shadow: 0 2px 0 rgba(0,0,0,0); /* Your transparent shadow */
  --input-pulse-color: var(--active, var(--primary));
}

@keyframes input-breathing {
  0% {
    box-shadow:
      0 0 0 1px var(--input-pulse-color),
      var(--input-anim-static-shadow),
      var(--input-base-shadow);
  }
  25% {
    box-shadow:
      0 0 6px 2px var(--input-pulse-color),
      var(--input-anim-static-shadow),
      var(--input-base-shadow);
  }
  50% {
    box-shadow:
      0 0 12px 3px var(--input-pulse-color),
      var(--input-anim-static-shadow),
      var(--input-base-shadow);
  }
  75% {
    box-shadow:
      0 0 6px 2px var(--input-pulse-color),
      var(--input-anim-static-shadow),
      var(--input-base-shadow);
  }
  100% {
    box-shadow:
      0 0 0 1px var(--input-pulse-color),
      var(--input-anim-static-shadow),
      var(--input-base-shadow);
  }
}

.dark .input-breathing:focus,
.dark .input-breathing.focus-visible {
  box-shadow:
    var(--input-anim-static-shadow),
    var(--input-base-shadow);
  outline: none;
}

.dark .input-breathing {
  animation: input-breathing 9000ms cubic-bezier(0.4, 0, 0.6, 1) infinite;
  transition: all 0ms ease-in-out;
}

/* Hide spinners from number inputs */
.hide-number-spinners::-webkit-outer-spin-button,
.hide-number-spinners::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.hide-number-spinners {
  -moz-appearance: textfield; /* Firefox */
}

.chromepanion-title-blade-glow {
  position: relative;
  z-index: 0;
  overflow: hidden;
  border-radius: 0.75em;
  background: var(--active); /* Keep the background static */
}

.chromepanion-title-blade-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background-image: conic-gradient(
    from 0deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 25%,
    rgba(255, 255, 255, 0.1) 50%
  );
  background-size: 200% 200%; /* Reduced size for sharper effect */
  animation: lightCircleMove 5s linear infinite; /* Faster animation for sharpness */
  pointer-events: none;
  z-index: 1;

  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  padding: 1px;
  box-sizing: border-box;
}

/* Keyframes for the light point circling the border */
@keyframes lightCircleMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.ball {
  position: absolute;
  border-radius: 100%;
  opacity: 0.7;
}

/* Ko-fi Widget Animation */
@keyframes shake {
  0%, 100% { transform: rotate(2deg); }
  25% { transform: rotate(1deg); }
  50% { transform: rotate(-1deg); }
  75% { transform: rotate(1deg); }
}

#kofi-widget {
  animation: shake 0.5s ease-in-out 6;
  display: inline-block;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#kofi-widget:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

#kofi-widget img {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#kofi-widget:hover img {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Settings Card Animations and Enhancements */
@keyframes cardHover {
  0% {
    transform: translateY(0px);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }
  100% {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.settings-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.settings-card:hover {
  animation: cardHover 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.settings-card-header {
  background: linear-gradient(135deg, var(--muted) 0%, var(--muted)/80 100%);
  backdrop-filter: blur(10px);
}

/* Subtle focus states for minimalist design */
.settings-card:focus-within {
  outline: none;
  box-shadow: 0 0 0 1px var(--border), 0 4px 12px -2px rgba(0, 0, 0, 0.08);
}

/* Refined focus styling for form controls */
.settings-card input:focus,
.settings-card textarea:focus,
.settings-card select:focus,
.settings-card [role="slider"]:focus,
.settings-card [role="combobox"]:focus {
  outline: none !important;
  border-color: var(--muted-foreground) !important;
  box-shadow: 0 0 0 1px var(--muted-foreground), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  ring: none !important;
}

/* Button focus styling */
.settings-card button:focus,
.settings-card button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 1px var(--muted-foreground), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  ring: none !important;
}

/* Radio button and checkbox focus */
.settings-card input[type="radio"]:focus,
.settings-card input[type="checkbox"]:focus {
  outline: none !important;
  box-shadow: 0 0 0 1px var(--muted-foreground) !important;
  ring: none !important;
}

/* Smooth transitions for all interactive elements */
.settings-card input,
.settings-card textarea,
.settings-card button,
.settings-card select,
.settings-card [role="slider"],
.settings-card [role="combobox"] {
  transition: all 0.2s ease-in-out;
}

/* Global focus ring override for subtle styling */
*:focus,
*:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 1px var(--muted-foreground) !important;
}

/* Specific overrides for common UI components */
input:focus,
textarea:focus,
select:focus,
button:focus,
[role="slider"]:focus,
[role="combobox"]:focus,
[role="button"]:focus,
[data-radix-collection-item]:focus {
  outline: none !important;
  border-color: var(--muted-foreground) !important;
  box-shadow: 0 0 0 1px var(--muted-foreground), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  --ring: none !important;
  --ring-color: transparent !important;
  --ring-offset-color: transparent !important;
}

/* Override Radix UI focus styles */
[data-state="open"]:focus,
[data-state="checked"]:focus,
[data-highlighted]:focus {
  outline: none !important;
  box-shadow: 0 0 0 1px var(--muted-foreground) !important;
}

/* Slider specific styling */
input[type="range"]:focus {
  outline: none !important;
  box-shadow: none !important;
}

input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px var(--muted-foreground) !important;
}

input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px var(--muted-foreground) !important;
}

/* Override any remaining blue focus styles from component libraries */
.focus\:ring-2:focus,
.focus\:ring-primary:focus,
.focus\:ring-blue-500:focus,
.focus\:border-primary:focus,
.focus\:border-blue-500:focus {
  --ring-color: var(--muted-foreground) !important;
  --ring-opacity: 0.3 !important;
  border-color: var(--muted-foreground) !important;
  box-shadow: 0 0 0 1px var(--muted-foreground) !important;
}

/* Ensure consistent focus styling for settings cards */
.settings-card *:focus,
.settings-card *:focus-visible {
  outline: none !important;
  --ring: none !important;
  --ring-color: transparent !important;
  --ring-offset-color: transparent !important;
}

/* Subtle hover states to complement the refined focus styling */
.settings-card input:hover,
.settings-card textarea:hover,
.settings-card select:hover,
.settings-card button:hover:not(:disabled) {
  border-color: var(--border) !important;
  box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.08) !important;
}

/* City Nights Text Effect Animation */
@keyframes cityLights {
  0% {
    color: hsl(230, 40%, 80%);
    text-shadow:
      0 0 1em hsla(320, 100%, 50%, 0.2),
      0 0 0.125em hsla(320, 100%, 60%, 0.3),
      -1em -0.125em 0.5em hsla(40, 100%, 60%, 0),
      1em 0.125em 0.5em hsla(200, 100%, 60%, 0);
  }
  
  30% { 
    color: hsl(230, 80%, 90%);
    text-shadow:
      0 0 1em hsla(320, 100%, 50%, 0.5),
      0 0 0.125em hsla(320, 100%, 60%, 0.5),
      -0.5em -0.125em 0.25em hsla(40, 100%, 60%, 0.2),
      0.5em 0.125em 0.25em hsla(200, 100%, 60%, 0.4);
  }
  
  40% { 
    color: hsl(230, 100%, 95%);
    text-shadow:
      0 0 1em hsla(320, 100%, 50%, 0.5),
      0 0 0.125em hsla(320, 100%, 90%, 0.5),
      -0.25em -0.125em 0.125em hsla(40, 100%, 60%, 0.2),
      0.25em 0.125em 0.125em hsla(200, 100%, 60%, 0.4);
  }
  
  70% {
    color: hsl(230, 80%, 90%);
    text-shadow:
      0 0 1em hsla(320, 100%, 50%, 0.5),
      0 0 0.125em hsla(320, 100%, 60%, 0.5),
      0.5em -0.125em 0.25em hsla(40, 100%, 60%, 0.2),
      -0.5em 0.125em 0.25em hsla(200, 100%, 60%, 0.4);
  }
  
  100% {
    color: hsl(230, 40%, 80%);
    text-shadow:
      0 0 1em hsla(320, 100%, 50%, 0.2),
      0 0 0.125em hsla(320, 100%, 60%, 0.3),
      1em -0.125em 0.5em hsla(40, 100%, 60%, 0),
      -1em 0.125em 0.5em hsla(200, 100%, 60%, 0);
  }
}

/* Base header title styles */
.header-title-glow {
  font-weight: 300;
  transition: all 0.3s ease;
}

/* Apply animation only when background is dark (via .dark class) */
.dark .header-title-glow {
  animation: cityLights 5s 750ms linear infinite;
}

/* Light theme default styling */
:not(.dark) .header-title-glow {
  color: var(--text);
  text-shadow: none;
}

/* Apply to specific header elements */
.configuration-title,
.chat-history-title {
  font-size: 1.5rem;
  font-weight: 300;
}

.thin-scrollbar::-webkit-scrollbar {
  width: 8px; /* Set the width of the scrollbar */
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(128, 128, 128, 0.5); /* Color of the scrollbar thumb */
  border-radius: 4px; /* Round the corners of the thumb */
}

.thin-scrollbar::-webkit-scrollbar-track {
  background-color: transparent; /* Make the scrollbar track transparent */
}

.thin-scrollbar {
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: rgba(128, 128, 128, 0.5) transparent; /* Thumb and track color for Firefox */
}
