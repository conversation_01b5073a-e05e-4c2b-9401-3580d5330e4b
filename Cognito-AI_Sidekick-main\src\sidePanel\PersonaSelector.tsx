import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useConfig } from './ConfigContext';
import { cn } from "@/src/background/util";
import { personaIcons, personaDescriptions } from './constants';

export const PersonaSelector = () => {
  const { config, updateConfig } = useConfig();
  const currentPersona = config?.persona || 'default';
  const personas = config?.personas || {};

  const handlePersonaChange = (value: string) => {
    updateConfig({ persona: value });
  };

  return (
    <div className="flex items-center">
      <Select value={currentPersona} onValueChange={handlePersonaChange}>
        <SelectTrigger className={cn(
          "w-auto min-w-[100px] border-none bg-transparent shadow-none",
          "text-apple-footnote font-medium text-foreground",
          "hover:bg-secondary/50 rounded-lg px-2 py-1 h-7",
          "focus:ring-2 focus:ring-primary/20 focus:border-primary/50"
        )}>
          <SelectValue placeholder="Select persona" />
        </SelectTrigger>
        <SelectContent className="bg-popover border border-border shadow-lg rounded-lg">
          <TooltipProvider delayDuration={300}>
            {Object.keys(personas).map((persona) => (
              <Tooltip key={persona}>
                <TooltipTrigger asChild>
                  <SelectItem
                    value={persona}
                    className={cn(
                      "text-apple-callout text-popover-foreground",
                      "hover:bg-accent hover:text-accent-foreground",
                      "focus:bg-accent focus:text-accent-foreground",
                      "cursor-pointer rounded-md"
                    )}
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-sm">{personaIcons[persona] || personaIcons.default}</span>
                      {persona === 'default' ? 'Chromepanion' : persona}
                    </div>
                  </SelectItem>
                </TooltipTrigger>
                <TooltipContent
                  side="right"
                  className="bg-popover text-popover-foreground border border-border max-w-xs"
                  sideOffset={8}
                >
                  <p className="text-apple-footnote">
                    {personaDescriptions[persona] || personaDescriptions.default}
                  </p>
                </TooltipContent>
              </Tooltip>
            ))}
          </TooltipProvider>
        </SelectContent>
      </Select>
    </div>
  );
};
