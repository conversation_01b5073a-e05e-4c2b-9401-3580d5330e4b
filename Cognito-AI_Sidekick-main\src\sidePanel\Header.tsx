import React, { useState, useEffect } from 'react';
import { FiX, FiTrash2, FiShare, FiChevronLeft, FiSettings, FiClock, FiMoreHorizontal, FiSun, FiMoon, FiShield, FiInfo } from 'react-icons/fi';
import { Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON> } from "react-icons/tb";
import { useConfig } from './ConfigContext';
import { cn } from "@/src/background/util";
import { Button } from "@/components/ui/button";
import { SettingsSheet } from './SettingsSheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";


import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import { Io<PERSON>erson, IoImageOutline, IoTextOutline } from "react-icons/io5";

import { BsFiletypeMd } from "react-icons/bs";

import {type Config } from "@/src/types/config";

import { PersonaSelector } from './PersonaSelector';
import { ModelDisplay } from './ModelDisplay';



interface WelcomeModalProps {
  isOpen: boolean;
  onClose: (open: boolean) => void;
  setSettingsMode: (mode: boolean) => void;
}
const WelcomeModal: React.FC<WelcomeModalProps> = ({ isOpen, onClose, setSettingsMode}) => (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent
      variant="themedPanel"
      className={cn(
          "[&>button]:hidden",
          "bg-card border border-border shadow-lg"
      )}
      style={{
        width: '20rem',
        height: '12rem',
        borderRadius: 'var(--radius-lg)',
        boxShadow: 'var(--shadow-lg)'
      }}
      onInteractOutside={(e) => e.preventDefault()}
    >
      <DialogHeader className="text-center p-4">
        <DialogTitle className="text-apple-title3 text-foreground">Welcome to Chromepanion</DialogTitle>
      </DialogHeader>
      <DialogDescription asChild>
        <div className="px-6 pb-6 text-center">
          <p className="text-apple-body text-muted-foreground mb-6">
            Get started by connecting to your AI models
          </p>
          <div className="flex justify-center">
            <Button
              variant="default"
              className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium"
              onClick={() => setSettingsMode(true)}
              aria-label="Open Settings"
            >
              Open Settings
            </Button>
          </div>
        </div>
      </DialogDescription>
    </DialogContent>
  </Dialog>
);



interface EditProfileDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  config: Config;
  updateConfig: (newConfig: Partial<Config>) => void;
}

const EditProfileDialog: React.FC<EditProfileDialogProps> = ({
  isOpen,
  onOpenChange,
  config,
  updateConfig,
}) => {
  const [currentUserName, setCurrentUserName] = useState(config?.userName || '');
  const [currentUserProfile, setCurrentUserProfile] = useState(config?.userProfile || '');

  useEffect(() => {
    if (isOpen) {
      setCurrentUserName(config?.userName || '');
      setCurrentUserProfile(config?.userProfile || '');
    }
  }, [isOpen, config?.userName, config?.userProfile]);

  const handleSave = () => {
    updateConfig({ userName: currentUserName, userProfile: currentUserProfile });
    onOpenChange(false);
    console.log("Profile updated!");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        variant="themedPanel"
        className="max-w-xs"
      >
        <DialogHeader className="px-6 py-4 border-b border-[var(--text)]/10">
          <DialogTitle className="text-lg font-semibold text-[var(--text)]">Edit Profile</DialogTitle>
          <DialogDescription className="text-sm text-[var(--text)] opacity-80">
            Set your display name and profile information. (For chat and export purposes)
          </DialogDescription>
        </DialogHeader>
        <div className="px-6 py-5 space-y-4">
          <div className="space-y-1.5">
            <Label htmlFor="username" className="text-sm font-medium text-[var(--text)] opacity-90">
              Username
            </Label>
            <Input
              id="username"
              value={currentUserName}
              onChange={(e) => setCurrentUserName(e.target.value)}
              className={cn(
                "focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]",
                "hover:border-[var(--active)] hover:brightness-98",
              )}
            />
          </div>
          <div className="space-y-1.5">
            <Label htmlFor="userprofile" className="text-sm font-medium text-[var(--text)] opacity-90">
              User Profile
            </Label>
            <Input
              id="userprofile"
              value={currentUserProfile}
              onChange={(e) => setCurrentUserProfile(e.target.value)}              
              className={cn(
                "focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]",
                "hover:border-[var(--active)] hover:brightness-98",
              )}
            />
          </div>
        </div>
        <DialogFooter className="px-6 py-4 border-t border-[var(--text)]/10">
          <Button
            variant="outline-subtle" // Use new variant
            size="sm" // Standardize size
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            variant="active-bordered" // Use new variant
            size="sm" // Standardize size
            onClick={handleSave}
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

interface HeaderProps {
  chatTitle?: string | null;
  settingsMode: boolean;
  setSettingsMode: (mode: boolean) => void;
  historyMode: boolean;
  setHistoryMode: (mode: boolean) => void;

  deleteAll: () => void | Promise<void>;
  reset: () => void;
  downloadImage: () => void;
  downloadJson: () => void;
  downloadText: () => void;
  downloadMarkdown: () => void;


}

export const Header: React.FC<HeaderProps> = ({
  chatTitle,
  settingsMode,
  setSettingsMode,
  historyMode,
  setHistoryMode,

  deleteAll,
  reset,
  downloadImage,
  downloadJson,
  downloadText,
  downloadMarkdown,


}) => {
  const { config, updateConfig } = useConfig();
  const [isEditProfileDialogOpen, setIsEditProfileDialogOpen] = useState(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);



  const visibleTitle = chatTitle && !settingsMode && !historyMode;
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const handleSheetOpenChange = (open: boolean) => {setIsSheetOpen(open);}

  const showBackButton = settingsMode || historyMode;

  const handleLeftButtonClick = () => {
    if (showBackButton) {
      setSettingsMode(false);
      setHistoryMode(false);

    }
  };

  const handleHistoryClick = () => {
    setHistoryMode(true);
  };

  const leftButtonLabel = showBackButton ? 'Back to Chat' : '';

  const handleDeleteAllWithConfirmation = () => {
    setIsDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    try {
      if (typeof deleteAll === 'function') {
        await deleteAll();
      } else {
        console.error("Header: deleteAll prop is not a function or undefined.", deleteAll);
      }
    } catch (error) {
      console.error("Error during deleteAll execution from header:", error);
    } finally {
      setIsDeleteConfirmOpen(false);
    }
  };
  const dropdownContentClasses = "z-50 min-w-[8rem] overflow-hidden rounded-lg border bg-popover p-1 text-popover-foreground shadow-lg animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2";
  const dropdownItemClasses = "flex cursor-default select-none items-center rounded-md px-3 py-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50";
  const dropdownSubTriggerClasses = "flex cursor-default select-none items-center rounded-md px-3 py-2 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent";
  const dropdownSeparatorClasses = "-mx-1 my-1 h-px bg-border";

  return (
    <TooltipProvider delayDuration={500}>
      <div
        className={cn(
          "bg-background/95 backdrop-blur-sm text-foreground",
          "border-b border-border",
          "sticky top-0 z-10",
        )}
      >
        <div className="flex items-center h-auto py-3 px-4">
          {/* Left Area - Persona Selector and Model Display */}
          <div className="flex justify-start items-center min-h-10 gap-3">
            {showBackButton ? (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    aria-label={leftButtonLabel}
                    variant="ghost"
                    size="sm"
                    className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center"
                    onClick={handleLeftButtonClick}
                  >
                    <FiX size="18px" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
                  {leftButtonLabel}
                </TooltipContent>
              </Tooltip>
            ) : (
              <>
                <PersonaSelector />
                <ModelDisplay />
              </>
            )}
          </div>

          {/* Middle Content Area */}
          <div className="flex-grow flex justify-center items-center overflow-hidden px-4">
            {visibleTitle && (
              <p className="text-apple-headline text-foreground whitespace-nowrap overflow-hidden text-ellipsis text-center">
                {chatTitle}
              </p>
            )}
            {settingsMode && (
              <div className="flex items-center justify-center">
                <p className="text-apple-title3 text-foreground">
                  Settings
                </p>
              </div>
            )}
            {historyMode && (
              <div className="flex items-center justify-center">
                <p className="text-apple-title3 text-foreground">
                  Chat History
                </p>
              </div>
            )}

          </div>

          {/* Right Button Area */}
          <div className="flex justify-end items-center min-h-10 gap-2">
            {!settingsMode && !historyMode && (
              <>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      aria-label="Reset Chat"
                      variant="ghost"
                      size="sm"
                      className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center group"
                      onClick={reset}
                    >
                      <TbReload
                        size="16px"
                        className="transition-transform duration-300 rotate-0 group-hover:rotate-180"
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
                    Reset Chat
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      aria-label="Chat History"
                      variant="ghost"
                      size="sm"
                      className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center"
                      onClick={handleHistoryClick}
                    >
                      <FiClock size="16px" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
                    Chat History
                  </TooltipContent>
                </Tooltip>

                {/* Three-dot Menu */}
                <DropdownMenuPrimitive.Root>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuPrimitive.Trigger asChild>
                        <Button
                          aria-label="More Options"
                          variant="ghost"
                          size="sm"
                          className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center"
                        >
                          <FiMoreHorizontal size="16px" />
                        </Button>
                      </DropdownMenuPrimitive.Trigger>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
                      More Options
                    </TooltipContent>
                  </Tooltip>
                  <DropdownMenuPrimitive.Portal>
                    <DropdownMenuPrimitive.Content
                      className={cn(
                        dropdownContentClasses,
                        "bg-popover text-popover-foreground border border-border shadow-xl min-w-[180px]"
                      )}
                      sideOffset={5}
                      align="end"
                    >
                      {/* Theme Toggle */}
                      <DropdownMenuPrimitive.Item
                        className={cn(
                          dropdownItemClasses,
                          "hover:bg-accent hover:text-accent-foreground cursor-pointer"
                        )}
                        onSelect={() => {
                          const currentTheme = config?.theme || 'light';
                          const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                          updateConfig({ theme: newTheme });
                        }}
                      >
                        <div className="flex items-center gap-3 w-full">
                          {config?.theme === 'dark' ? <FiSun className="h-4 w-4" /> : <FiMoon className="h-4 w-4" />}
                          <span>Theme</span>
                          <span className="ml-auto text-xs text-muted-foreground">
                            {config?.theme === 'dark' ? 'Light' : 'Dark'}
                          </span>
                        </div>
                      </DropdownMenuPrimitive.Item>

                      {/* Settings */}
                      <DropdownMenuPrimitive.Item
                        className={cn(
                          dropdownItemClasses,
                          "hover:bg-accent hover:text-accent-foreground cursor-pointer"
                        )}
                        onSelect={() => setSettingsMode(true)}
                      >
                        <div className="flex items-center gap-3 w-full">
                          <FiSettings className="h-4 w-4" />
                          <span>Settings</span>
                        </div>
                      </DropdownMenuPrimitive.Item>

                      <DropdownMenuPrimitive.Separator
                        className={cn(
                          dropdownSeparatorClasses,
                          "bg-border"
                        )}
                      />

                      {/* Share Options Submenu */}
                      <DropdownMenuPrimitive.Sub>
                        <DropdownMenuPrimitive.SubTrigger
                          className={cn(
                            dropdownSubTriggerClasses,
                            "hover:bg-accent hover:text-accent-foreground cursor-pointer"
                          )}
                        >
                          <div className="flex items-center gap-3 w-full">
                            <FiShare className="h-4 w-4" />
                            <span>Share Options</span>
                            <FiChevronLeft className="ml-auto h-4 w-4 rotate-180" />
                          </div>
                        </DropdownMenuPrimitive.SubTrigger>
                        <DropdownMenuPrimitive.Portal>
                          <DropdownMenuPrimitive.SubContent
                            className={cn(
                              dropdownContentClasses,
                              "bg-popover text-popover-foreground border border-border shadow-lg"
                            )}
                            sideOffset={2}
                            alignOffset={-5}
                          >
                            {/* Edit Profile */}
                            <DropdownMenuPrimitive.Item
                              className={cn(dropdownItemClasses, "hover:bg-accent hover:text-accent-foreground cursor-pointer")}
                              onSelect={() => setIsEditProfileDialogOpen(true)}
                            >
                              <div className="flex items-center gap-3 w-full">
                                <IoPerson className="h-4 w-4" />
                                <span>Edit Profile</span>
                              </div>
                            </DropdownMenuPrimitive.Item>

                            <DropdownMenuPrimitive.Separator
                              className={cn(dropdownSeparatorClasses, "bg-border")}
                            />

                            {/* Export Options */}
                            <DropdownMenuPrimitive.Item
                              className={cn(dropdownItemClasses, "hover:bg-accent hover:text-accent-foreground cursor-pointer")}
                              onSelect={downloadMarkdown}
                            >
                              <div className="flex items-center gap-3 w-full">
                                <BsFiletypeMd className="h-4 w-4" />
                                <span>Export as Markdown</span>
                              </div>
                            </DropdownMenuPrimitive.Item>
                            <DropdownMenuPrimitive.Item
                              className={cn(dropdownItemClasses, "hover:bg-accent hover:text-accent-foreground cursor-pointer")}
                              onSelect={downloadText}
                            >
                              <div className="flex items-center gap-3 w-full">
                                <IoTextOutline className="h-4 w-4" />
                                <span>Export as Text</span>
                              </div>
                            </DropdownMenuPrimitive.Item>
                            <DropdownMenuPrimitive.Item
                              className={cn(dropdownItemClasses, "hover:bg-accent hover:text-accent-foreground cursor-pointer")}
                              onSelect={downloadJson}
                            >
                              <div className="flex items-center gap-3 w-full">
                                <TbJson className="h-4 w-4" />
                                <span>Export as JSON</span>
                              </div>
                            </DropdownMenuPrimitive.Item>
                            <DropdownMenuPrimitive.Item
                              className={cn(dropdownItemClasses, "hover:bg-accent hover:text-accent-foreground cursor-pointer")}
                              onSelect={downloadImage}
                            >
                              <div className="flex items-center gap-3 w-full">
                                <IoImageOutline className="h-4 w-4" />
                                <span>Export as Image</span>
                              </div>
                            </DropdownMenuPrimitive.Item>
                          </DropdownMenuPrimitive.SubContent>
                        </DropdownMenuPrimitive.Portal>
                      </DropdownMenuPrimitive.Sub>

                      <DropdownMenuPrimitive.Separator
                        className={cn(
                          dropdownSeparatorClasses,
                          "bg-border"
                        )}
                      />

                      {/* Privacy - Placeholder */}
                      <DropdownMenuPrimitive.Item
                        className={cn(
                          dropdownItemClasses,
                          "opacity-50 cursor-not-allowed"
                        )}
                        disabled
                      >
                        <div className="flex items-center gap-3 w-full">
                          <FiShield className="h-4 w-4" />
                          <span>Privacy</span>
                          <span className="ml-auto text-xs text-muted-foreground">Soon</span>
                        </div>
                      </DropdownMenuPrimitive.Item>

                      {/* About - Placeholder */}
                      <DropdownMenuPrimitive.Item
                        className={cn(
                          dropdownItemClasses,
                          "opacity-50 cursor-not-allowed"
                        )}
                        disabled
                      >
                        <div className="flex items-center gap-3 w-full">
                          <FiInfo className="h-4 w-4" />
                          <span>About</span>
                          <span className="ml-auto text-xs text-muted-foreground">Soon</span>
                        </div>
                      </DropdownMenuPrimitive.Item>
                    </DropdownMenuPrimitive.Content>
                  </DropdownMenuPrimitive.Portal>
                </DropdownMenuPrimitive.Root>
              </>
            )}
            {historyMode && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    aria-label="Delete All History"
                    variant="ghost"
                    size="sm"
                    className="text-[var(--text)] rounded-md"
                    onClick={handleDeleteAllWithConfirmation}
                  >
                    <FiTrash2 size="18px" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]">
                  Delete All
                </TooltipContent>
              </Tooltip>
            )}

          </div>
        </div>

        {(!config?.models || config.models.length === 0) && !settingsMode && !historyMode && (
           <WelcomeModal isOpen={true} setSettingsMode={setSettingsMode} onClose={() => {}} />
        )}

        <SettingsSheet
          isOpen={isSheetOpen}
          onOpenChange={handleSheetOpenChange}
          config={config}
          updateConfig={updateConfig}
          setSettingsMode={setSettingsMode}
          setHistoryMode={setHistoryMode}

        />

        <EditProfileDialog
          isOpen={isEditProfileDialogOpen}
          onOpenChange={setIsEditProfileDialogOpen}
          config={config}
          updateConfig={updateConfig}
        />

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
          <DialogContent
            variant="themedPanel"
            className="max-w-sm"
          >
            <DialogHeader className="px-6 py-4 border-b border-[var(--text)]/10">
              <DialogTitle className="text-lg font-semibold text-[var(--text)]">Confirm Deletion</DialogTitle>
              <DialogDescription className="text-sm text-[var(--text)] opacity-90">
                Are you sure you want to delete all chat history? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="px-6 py-4 border-t border-[var(--text)]/10">
              <Button
                variant="outline-subtle"
                size="sm"
                onClick={() => setIsDeleteConfirmOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleConfirmDelete}
              >
                Delete All
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
};