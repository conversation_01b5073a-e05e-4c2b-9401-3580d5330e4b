<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Search Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #1565c0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input {
            width: 300px;
            padding: 8px;
            margin: 0 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Google Search Functionality Test</h1>
    
    <div class="test-container">
        <h2>Test Google Search</h2>
        <div>
            <input type="text" id="searchQuery" placeholder="Enter search query" value="JavaScript tutorials">
            <button onclick="testSearch()" id="searchBtn">Test Search</button>
        </div>
        <div id="searchResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // Mock config object for testing
        const testConfig = {
            webMode: 'Google',
            serpMaxLinksToVisit: 2,
            webLimit: 16
        };

        async function testSearch() {
            const query = document.getElementById('searchQuery').value;
            const resultDiv = document.getElementById('searchResult');
            const searchBtn = document.getElementById('searchBtn');
            
            if (!query.trim()) {
                alert('Please enter a search query');
                return;
            }

            searchBtn.disabled = true;
            searchBtn.textContent = 'Searching...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Performing Google search...';

            try {
                // Test the Google search functionality
                const result = await testGoogleSearch(query, testConfig);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `Search completed successfully!\n\nResults:\n${result}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Search failed:\n${error.message}\n\nStack trace:\n${error.stack}`;
                console.error('Search test failed:', error);
            } finally {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Test Search';
            }
        }

        // Simplified version of the webSearch function for testing
        async function testGoogleSearch(query, config) {
            console.log('[testGoogleSearch] Starting search for:', query);
            
            const baseUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}&hl=en&gl=us&num=10&start=0&safe=off&filter=0`;
            console.log('[testGoogleSearch] URL:', baseUrl);

            const response = await fetch(baseUrl, {
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Cache-Control': 'max-age=0',
                    'Referer': 'https://www.google.com/',
                }
            });

            console.log('[testGoogleSearch] Response status:', response.status, response.statusText);
            console.log('[testGoogleSearch] Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                const errorText = await response.text();
                console.error('[testGoogleSearch] Error response:', errorText.substring(0, 1000));
                throw new Error(`Google search failed with status ${response.status}: ${response.statusText}`);
            }

            const htmlString = await response.text();
            console.log('[testGoogleSearch] HTML length:', htmlString.length);
            console.log('[testGoogleSearch] HTML preview:', htmlString.substring(0, 500));

            // Check for blocking
            if (htmlString.includes('captcha') || htmlString.includes('unusual traffic') || htmlString.includes('blocked')) {
                throw new Error('Google search blocked - CAPTCHA or unusual traffic detected');
            }

            const parser = new DOMParser();
            const htmlDoc = parser.parseFromString(htmlString, 'text/html');

            // Parse search results
            const searchResults = [];
            const resultSelectors = ['div.g', 'div.MjjYud', 'div.hlcw0c', 'div.kvH3mc', 'div.tF2Cxc', 'div.yuRUbf'];
            
            let resultElements = [];
            for (const selector of resultSelectors) {
                const elements = Array.from(htmlDoc.querySelectorAll(selector));
                if (elements.length > 0) {
                    resultElements = elements;
                    console.log(`[testGoogleSearch] Found ${elements.length} results using selector: ${selector}`);
                    break;
                }
            }

            if (resultElements.length === 0) {
                resultElements = Array.from(htmlDoc.querySelectorAll('div[data-ved], div[data-hveid]'));
                console.log(`[testGoogleSearch] Fallback: Found ${resultElements.length} results using data attributes`);
            }

            resultElements.slice(0, 5).forEach((result, index) => {
                try {
                    let linkEl = result.querySelector('a[href^="http"]') || 
                                result.querySelector('a[href^="/url?q="]') ||
                                result.querySelector('a[href]');
                    
                    let url = linkEl?.getAttribute('href');
                    
                    if (url && url.startsWith('/url?q=')) {
                        const urlParams = new URLSearchParams(url.substring(6));
                        url = urlParams.get('q') || url;
                    }
                    
                    const titleEl = result.querySelector('h3') || 
                                   result.querySelector('h2') ||
                                   result.querySelector('[role="heading"]');
                    
                    const title = titleEl?.textContent?.trim() || '';
                    
                    if (title && url && url.startsWith('http') && !url.includes('google.com/search')) {
                        searchResults.push({ title, url });
                        console.log(`[testGoogleSearch] Result ${index + 1}: ${title}`);
                    }
                } catch (error) {
                    console.warn(`[testGoogleSearch] Error parsing result ${index + 1}:`, error);
                }
            });

            if (searchResults.length === 0) {
                throw new Error('No search results found - Google may have changed their HTML structure');
            }

            return `Found ${searchResults.length} search results:\n\n` + 
                   searchResults.map((result, i) => `${i + 1}. ${result.title}\n   ${result.url}`).join('\n\n');
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('Test page loaded. Ready to test Google search functionality.');
        });
    </script>
</body>
</html>
