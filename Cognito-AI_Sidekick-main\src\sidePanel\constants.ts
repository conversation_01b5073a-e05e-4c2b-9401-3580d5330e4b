// Persona icons using emoji/unicode characters for unique visual identity
export const personaIcons: {
  Scholar: string;
  Executive: string;
  Storyteller: string;
  Skeptic: string;
  Mentor: string;
  Investigator: string;
  Pragmatist: string;
  Enthusiast: string;
  Curator: string;
  Friend: string;
  Spike: string;
  default: string;
  [key: string]: string | undefined;
} = {
  Scholar: '🎓',
  Executive: '💼',
  Storyteller: '📚',
  Skeptic: '🤔',
  Mentor: '🧭',
  Investigator: '🔍',
  Pragmatist: '⚙️',
  Enthusiast: '⚡',
  Curator: '🎨',
  Friend: '😊',
  Spike: '🚀',
  default: '🤖'
};

// Persona descriptions for tooltips
export const personaDescriptions: {
  Scholar: string;
  Executive: string;
  Storyteller: string;
  Skeptic: string;
  Mentor: string;
  Investigator: string;
  Pragmatist: string;
  Enthusiast: string;
  Curator: string;
  Friend: string;
  Spike: string;
  default: string;
  [key: string]: string | undefined;
} = {
  Scholar: 'Analytical academic researcher with formal tone. Provides structured analysis with citations and source credibility assessment.',
  Executive: 'Strategic business leader with direct, results-oriented tone. Delivers concise insights with actionable recommendations.',
  Storyteller: 'Engaging narrative creator with vivid language. Weaves information into compelling stories using metaphors and examples.',
  Skeptic: 'Critical analyst with questioning tone. Highlights biases, contradictions, and missing information in sources.',
  Mentor: 'Educational guide with encouraging tone. Breaks down complex topics with step-by-step explanations and learning tips.',
  Investigator: 'Methodical fact-checker with systematic approach. Verifies information credibility and distinguishes facts from claims.',
  Pragmatist: 'Solution-focused analyst with practical tone. Emphasizes actionable insights and real-world implementation strategies.',
  Enthusiast: 'Energetic discoverer with exciting tone. Highlights fascinating developments and breakthrough insights with enthusiasm.',
  Curator: 'Sophisticated synthesizer with refined tone. Provides premium insights with elegant analysis and polished presentation.',
  Friend: 'Casual conversationalist with friendly tone. Shares discoveries in approachable, relatable manner like talking to a friend.',
  Spike: 'Versatile executor with concise, sharp tone. Turns prompts into actionable results with direct, no-fluff approach.',
  default: 'AI assistant ready to help with your search and analysis needs.'
};

export const personaImages: {
  Scholar: string;
  Executive: string;
  Storyteller: string;
  Skeptic: string;
  Mentor: string;
  Investigator: string;
  Pragmatist: string;
  Enthusiast: string;
  Curator: string;
  Friend: string;
  Spike: string;
  default: string;
  [key: string]: string | undefined;
} = {
  Scholar: 'assets/images/chromepanion.png',
  Executive: 'assets/images/chromepanion.png',
  Storyteller: 'assets/images/chromepanion.png',
  Skeptic: 'assets/images/chromepanion.png',
  Mentor: 'assets/images/chromepanion.png',
  Investigator: 'assets/images/chromepanion.png',
  Pragmatist: 'assets/images/chromepanion.png',
  Enthusiast: 'assets/images/chromepanion.png',
  Curator: 'assets/images/chromepanion.png',
  Friend: 'assets/images/chromepanion.png',
  Spike: 'assets/images/chromepanion.png',
  default: 'assets/images/chromepanion.png'
};

